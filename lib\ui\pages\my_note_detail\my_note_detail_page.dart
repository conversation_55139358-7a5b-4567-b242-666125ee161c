import 'dart:io';
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:just_audio/just_audio.dart';
import 'package:lottie/lottie.dart';
import 'package:markdown_quill/markdown_quill.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/my_note_detail/cubit/my_note_detail_state.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';

class MyNoteDetailPage extends StatefulWidget {
  static const routeName = 'note_detail';

  final NoteModel noteModel;
  final bool isCommunityNote;
  final bool isTablet;
  final NoteDetailPageFrom from;
  final List<String>? savedTabs;

  const MyNoteDetailPage({
    super.key,
    required this.noteModel,
    required this.isTablet,
    required this.from,
    this.isCommunityNote = false,
    this.savedTabs,
  });

  @override
  State<MyNoteDetailPage> createState() => _MyNoteDetailPageState();
}

class _MyNoteDetailPageState
    extends BasePageStateDelegate<MyNoteDetailPage, MyNoteDetailCubit>
    with SingleTickerProviderStateMixin {
  late YoutubePlayerController _controllerVideo;
  late TabController _tabController;
  List<String> _tabList = [S.current.summary]; // Default tab
  TextEditingController folderNameController = TextEditingController();
  late List<FolderModel> listFolder;
  late ValueNotifier<String> chooseFolderName;
  late ValueNotifier<int> _tabIndexNotifier;
  late final ValueNotifier<TextDirection> _titleTextDirection =
      ValueNotifier(TextDirection.ltr);
  late final FocusNode _focusNodeQuill;
  final FocusNode _focusNodeTitle = FocusNode();
  final _overflowMenuKey = GlobalKey();
  final GlobalKey<UpdatableNoteSharingViewState> _sharingViewKey =
      GlobalKey<UpdatableNoteSharingViewState>();
  final _localService = GetIt.instance.get<LocalService>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (Platform.isAndroid) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
            overlays: [SystemUiOverlay.top]);
      }
    });
    cubit.resetState();
    cubit.fetchNoteDetail(
      widget.noteModel.backendNoteId,
      widget.noteModel,
      isCommunityNote: widget.isCommunityNote,
    );
    cubit.initNoteBoxListener(noteModel: widget.noteModel);
    _initControllers();
    _initControllersVideo();
    listFolder = [
      FolderModel(
          id: cubit.allNoteId, folderName: S.current.all_note, backendId: ''),
      ...HiveFolderService.getAllFolders()
    ];
    chooseFolderName = ValueNotifier(
      HiveFolderService.getNameFolderByBackEndId(widget.noteModel.folderId),
    );
    _tabIndexNotifier = ValueNotifier(0);
    _focusNodeQuill = FocusNode();
    cubit.getTitleController.text = widget.noteModel.title;
    AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_show);
  }

  void _initControllers() async {
    // Get all available tabs
    List<String> allTabs = widget.savedTabs ??
        await GetIt.instance.get<LocalService>().loadSelectedItems();

    // Filter tabs based on note type
    _tabList = allTabs.where((tab) {
      // Always show summary tab
      if (tab == S.current.summary) return true;

      // Only show document tab for document type notes
      if (tab == S.current.document_tab) {
        return widget.noteModel.type == NoteType.document.backendType;
      }

      // Show all other tabs
      return true;
    }).toList();

    // Initialize TabController with dynamic length
    _tabController = TabController(
      length: _tabList.length,
      vsync: this,
    );

    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        _tabIndexNotifier.value = _tabController.index;
        debugPrint('tabIndexNotifier: ${_tabIndexNotifier.value}');
      } else if (_tabController.animation != null) {
        _tabIndexNotifier.value = _tabController.animation!.value.round();
      }
      FocusScope.of(context).unfocus();
    });
  }

  void _initControllersVideo() {
    _controllerVideo = YoutubePlayerController(
      params: const YoutubePlayerParams(
        showFullscreenButton: true,
        playsInline: false,
        pointerEvents: PointerEvents.initial,
        showControls: true,
        strictRelatedVideos: false,
      ),
    )..cueVideoById(videoId: widget.noteModel.youtubeId);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _controllerVideo.close();
    if (!widget.isTablet) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }
    _tabIndexNotifier.dispose();
    _titleTextDirection.dispose();
    SearchToolbar.hide();
    super.dispose();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<MyNoteDetailCubit, MyNoteDetailState>(
      listenWhen: (previous, current) =>
          previous.deleteNoteEvent != current.deleteNoteEvent ||
          previous.oneShotEvent != current.oneShotEvent ||
          previous.translateNoteEvent != current.translateNoteEvent,
      listener: (context, state) {
        if (state.deleteNoteEvent == DeleteNoteEvent.success) {
          Navigator.pop(context);
        }
        _handleOneShotEvent(state.oneShotEvent);
        _handleTranslateEvent(state.translateNoteEvent);
        cubit.resetState();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
      buildWhen: (previous, current) => previous.hasEdits != current.hasEdits,
      builder: (context, state) {
        return PopScope(
          canPop: !cubit.hasUnsavedChanges,
          child: Center(
            child: YoutubePlayerScaffold(
              autoFullScreen: false,
              controller: _controllerVideo,
              aspectRatio: 16 / 9,
              defaultOrientations: cubit.appCubit.isTablet
                  ? AppConstants.allOrientations
                  : AppConstants.portraitOnly,
              fullscreenOrientations: AppConstants.allOrientations,
              enableFullScreenOnVerticalDrag: false,
              builder: (context, player) {
                return Scaffold(
                  backgroundColor: context.colorScheme.mainBackground,
                  resizeToAvoidBottomInset: false,
                  appBar: AppBarWidget(
                    backgroundColor: context.colorScheme.mainBackground,
                    isShowLeftButton: true,
                    actions: [
                      if (widget.isCommunityNote == false) ...[
                        _buildActionButtons(),
                      ]
                    ],
                    customTitle: _buildTitle(),
                    onPressed: _onBackPressed,
                  ),
                  body: Stack(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildTabBar(),
                          _buildTabBarView(player),
                        ],
                      ),

                      /// UI CHAT
                      KeyboardVisibilityBuilder(
                        builder: (context, isKeyboardVisible) {
                          return isKeyboardVisible
                              ? const SizedBox.shrink()
                              : ValueListenableBuilder(
                                  valueListenable: cubit.observerBoxNote,
                                  builder: (context, box, child) =>
                                      _buildChatButton(context, box),
                                );
                        },
                      ),

                      /// Audio Player
                      KeyboardVisibilityBuilder(
                        builder: (context, isKeyboardVisible) {
                          return _shouldShowAudioPlayer && !isKeyboardVisible
                              ? _buildAudioPlayerContainer()
                              : const SizedBox.shrink();
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildTitle() {
    return BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
      buildWhen: (previous, current) =>
          previous.isVisibleKeyboard == current.isVisibleKeyboard,
      builder: (context, state) {
        final originalText = cubit.getTitleController.text;
        final displayText = originalText.length > 24
            ? '${originalText.substring(0, 24)}...'
            : originalText;

        if (widget.isCommunityNote) {
          // For community notes, use DirectionalText for read-only display
          return IntrinsicWidth(
            child: DirectionalText(
              text: displayText,
              detectFromContent: true,
              style: TextStyle(
                fontSize: context.isTablet ? 17 : 15.sp,
                color: context.colorScheme.mainPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          );
        }

        return ValueListenableBuilder<TextDirection>(
          valueListenable: _titleTextDirection,
          builder: (context, textDirection, _) {
            // Update text direction on build
            if (_titleTextDirection.value !=
                RtlUtils.getTextDirectionForContent(originalText)) {
              // We're inside a builder so this is safe and won't cause rebuild loops
              _titleTextDirection.value =
                  RtlUtils.getTextDirectionForContent(originalText);
            }

            return Directionality(
              textDirection: textDirection,
              child: IntrinsicWidth(
                child: TextFormField(
                  focusNode: _focusNodeTitle,
                  enabled: true,
                  controller: TextEditingController.fromValue(
                    cubit.getTitleController.value.copyWith(
                      text:
                          _focusNodeTitle.hasFocus ? originalText : displayText,
                      selection: cubit.getTitleController.selection,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: context.isTablet ? 17 : 15.sp,
                    color: context.colorScheme.mainPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: textDirection == TextDirection.rtl
                      ? TextAlign.right
                      : TextAlign.left,
                  textDirection: textDirection,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                  onChanged: (newTitle) {
                    cubit.getTitleController.value =
                        cubit.getTitleController.value.copyWith(
                      text: newTitle,
                    );
                    // Update text direction without setState
                    _titleTextDirection.value =
                        RtlUtils.getTextDirectionForContent(newTitle);
                  },
                  onFieldSubmitted: (newTitle) {
                    final note =
                        cubit.observerBoxNote.value.get(widget.noteModel.id);
                    final trimmedTitle = newTitle.trim();
                    if (trimmedTitle.isNotEmpty &&
                        note != null &&
                        note.title != trimmedTitle) {
                      cubit.onEditNote(
                        note: note,
                        title: trimmedTitle,
                      );
                    }
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTabBarView(Widget player) {
    // Create tab view children based on saved tab list
    final List<Widget> tabViewChildren = _tabList.map((tabName) {
      final widgetBuilder = _tabWidgetsMap[tabName];
      if (widgetBuilder != null) {
        return widgetBuilder(player);
      }
      // Fallback widget if tab type not found
      return const SizedBox.shrink();
    }).toList();

    return Expanded(
      child: BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
        buildWhen: (previous, current) =>
            previous.isFetchingNoteDataFromServer !=
            current.isFetchingNoteDataFromServer,
        builder: (context, state) {
          if (state.isFetchingNoteDataFromServer == true) {
            return SizedBox(
              height: MediaQuery.of(context).size.height - 150.h,
              child: Center(
                child: CupertinoActivityIndicator(
                  radius: 20.r,
                ),
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            physics: const NeverScrollableScrollPhysics(),
            children: tabViewChildren,
          );
        },
      ),
    );
  }

  // Map to store widgets for each tab type
  Map<String, Widget Function(Widget player)> get _tabWidgetsMap => {
        S.current.summary: (player) => KeepAlivePage(
              child: BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
                buildWhen: (previous, current) =>
                    previous.isRefreshingSummary != current.isRefreshingSummary,
                builder: (context, state) {
                  final note =
                      cubit.observerBoxNote.value.get(widget.noteModel.id);
                  return SummaryPage(
                    noteModel: note ?? widget.noteModel,
                    isCommunityNote: widget.isCommunityNote,
                    tabController: _tabController,
                    controllerVideo: _controllerVideo,
                    player: player,
                    focusNode: _focusNodeQuill,
                    quillController: cubit.getQuillSummaryController,
                    didFetchNoteDataFail: cubit.didFetchNoteDataFail,
                  );
                },
              ),
            ),
        S.current.transcript: (player) => KeepAlivePage(
              child: BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
                buildWhen: (previous, current) =>
                    previous.isRefreshingTranscript !=
                    current.isRefreshingTranscript,
                builder: (context, state) {
                  final note = widget.isCommunityNote
                      ? widget.noteModel
                      : cubit.observerBoxNote.value.get(widget.noteModel.id);
                  return TabTranscriptWidget(
                    noteModel: note ?? widget.noteModel,
                    isCommunityNote: widget.isCommunityNote,
                    quillTranscriptController:
                        cubit.getQuillTranscriptController,
                    cubit: cubit,
                    audioPlayer: (_audioPlayerKey.currentState as AudioPlayerInterface?)?.audioPlayer,
                  );
                },
              ),
            ),
        S.current.slide_show: (player) => KeepAlivePage(
              child: TabSlideShowWidget(
                noteModel: widget.noteModel,
              ),
            ),
        S.current.document_tab: (player) {
          if (widget.noteModel.type == NoteType.document.backendType) {
            return KeepAlivePage(
              child: TabDocumentWidget(
                note: widget.noteModel,
              ),
            );
          }
          return const SizedBox.shrink();
        },
        S.current.mind_map: (player) => KeepAlivePage(
              child: TabMindMapWidget(
                widget: widget,
                cubit: cubit,
              ),
            ),
        S.current.shorts: (player) => KeepAlivePage(
              child: TabShortsWidget(
                noteModel: widget.noteModel,
              ),
            ),
        S.current.podcast: (player) => KeepAlivePage(
              child: TabPodcastWidget(
                noteModel: widget.noteModel,
              ),
            ),
        S.current.flashcard: (player) => KeepAlivePage(
              child: TabFlashCardWidget(
                widget: widget,
                cubit: cubit,
              ),
            ),
        S.current.quizzes: (player) => KeepAlivePage(
              child: TabQuizWidget(
                widget: widget,
                cubit: cubit,
              ),
            ),
      };

  Widget _buildTabBar() {
    return TabBar(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 8.w,
        bottom: 16.h,
      ),
      tabAlignment: TabAlignment.start,
      controller: _tabController,
      isScrollable: true,
      onTap: _tabController.animateTo,
      tabs: _tabList.map((entry) => _buildTabItem(entry)).toList(),
      indicator: _buildTabIndicator(),
      labelColor: context.colorScheme.mainPrimary,
      unselectedLabelColor: context.colorScheme.mainGray,
      indicatorColor: Colors.transparent,
      dividerColor: Colors.transparent,
      indicatorPadding: EdgeInsets.zero,
      splashFactory: NoSplash.splashFactory,
      labelPadding: EdgeInsets.only(right: 8.w),
      indicatorWeight: 0,
    );
  }

  void _showModalBottomSheetFolder(BuildContext context) {
    final double maxHeight = MediaQuery.of(context).size.height -
        MediaQuery.of(context).padding.top -
        kToolbarHeight;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: maxHeight,
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: context.colorScheme.mainNeutral,
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(20.r),
                  ),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 24.h,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonText(
                          S.current.add_to,
                          style: TextStyle(
                            fontSize: cubit.appCubit.isTablet ? 22 : 20.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: SvgPicture.asset(
                            Assets.icons.icCloseWhite,
                            width: 24.w,
                            height: 24.h,
                            colorFilter: ColorFilter.mode(
                              context.colorScheme.mainGray,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ],
                    ),
                    AppConstants.kSpacingItem12,
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: listFolder.length,
                      itemBuilder: (context, index) {
                        final folder = listFolder[index];
                        return FolderItemWidget(
                          folder: folder,
                          noteId: widget.noteModel.id,
                          allNoteId: cubit.allNoteId,
                          onTap: () async {
                            final note =
                                HiveService().noteBox.get(widget.noteModel.id)!;
                            cubit.addNoteToFolder(
                              folder,
                              note,
                              chooseFolderName,
                            );
                            Navigator.of(context).pop();
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _onBackPressed() async {
    if (cubit.hasUnsavedChanges) {
      showNewCupertinoDialog(
        context: context,
        title: S.current.discard_changes,
        message: S.current.content_discard_changes_note,
        image: Assets.icons.icDiscardChanges,
        cancelButton: S.current.cancel,
        confirmButton: S.current.discard,
        onCancel: () {},
        onConfirm: () async {
          cubit.discardChanges();
          await _handleBackNavigation();
        },
      );
    } else {
      await _handleBackNavigation();
    }
  }

  Future<void> _handleBackNavigation() async {
    final note = cubit.observerBoxNote.value.get(widget.noteModel.id);
    cubit.onNoteSummaryBack();
    if (GetIt.I.get<LocalService>().getIsFirstCreateNote() &&
        cubit.appCubit.isUserProlite() &&
        note?.noteStatus == NoteStatus.success) {
      Navigator.of(context).pushReplacement(
        CupertinoPageRoute(
          builder: (context) => const PurchaseLifeTimePageOldVer(
            from: PurchaseLifetimeFrom.myNoteDetail,
          ),
          fullscreenDialog: true,
        ),
      );
      GetIt.I.get<LocalService>().setIsFirstCreateNote(false);
    } else {
      Navigator.pop(context);
      final reviewAvailable = await InAppReview.instance.isAvailable();
      if (reviewAvailable && cubit.appCubit.isPendingShowRate) {
        InAppReview.instance.requestReview();
        cubit.appCubit.setIsPendingShowRate(false);
      }
    }
  }

  bool get _shouldShowAudioPlayer =>
      (widget.noteModel.type == 'audio' ||
          widget.noteModel.type == 'audio_file') &&
      (widget.noteModel.audioFilePath.isNotEmpty ||
          widget.noteModel.audioUrl.isNotEmpty);

  // Global key for accessing the audio player widget
  final GlobalKey<State<AudioPlayerWidget>> _audioPlayerKey =
      GlobalKey<State<AudioPlayerWidget>>();

  // Getter to access the audio player instance
  AudioPlayer? get audioPlayer {
    final state = _audioPlayerKey.currentState;
    if (state is AudioPlayerInterface) {
      return (state as AudioPlayerInterface).audioPlayer;
    }
    return null;
  }

  Widget _buildAudioPlayerContainer() {
    return KeyboardVisibilityBuilder(builder: (context, isKeyboardVisible) {
      return Positioned(
        bottom: (Platform.isAndroid
                ? MediaQuery.of(context).padding.bottom + 76
                : 100) +
            (isKeyboardVisible ? kToolbarHeight : 0),
        right: 16.w,
        left: 16.w,
        child: AudioPlayerWidget(
          audioPlayerKey: _audioPlayerKey,
          fullScreen: false,
          isCommunityNote: widget.isCommunityNote,
          audioFilePath: widget.noteModel.audioFilePath,
          audioUrl: widget.noteModel.audioUrl.isNotEmpty
              ? widget.noteModel.audioUrl
              : null,
          backgroundColor: context.colorScheme.mainNeutral,
        ),
      );
    });
  }

  void _navigateToIAP() {
    Navigator.of(context).pushNamed(
      PurchasePage.routeName,
      arguments: {
        EventKey.from: PurchasePageFrom.iapTranslateNote,
      },
    ).then((didPurchaseSuccess) => {
          if (didPurchaseSuccess == true) {cubit.onTranslateNote()}
        });
  }

  void _handleOneShotEvent(MyNoteDetailOneShotEvent event) {
    switch (event) {
      case MyNoteDetailOneShotEvent.onShowIAPFromTranslateNote:
        _navigateToIAP();
        break;
      default:
        break;
    }
  }

  void _handleTranslateEvent(TranslateNoteEvent event) {
    switch (event) {
      case TranslateNoteEvent.success:
        closeDialog();
        showBlurToast(
          context,
          message: S.current.translation_completed,
          duration: const Duration(seconds: 2),
        );
        Future.delayed(const Duration(seconds: 2), () {
          // ignore: use_build_context_synchronously
          Navigator.pop(context);
        });
        break;
      case TranslateNoteEvent.loading:
        showTranslatingDialog(context, title: S.current.translating_note);
        break;
      case TranslateNoteEvent.error:
        closeDialog();
        CommonDialogs.showErrorDialog(context,
            title: S.current.translation_failed);
        break;
      case TranslateNoteEvent.initial:
        showOutputLanguageDialog(
          context,
          title: S.current.translate_note,
          targetLanguageCode: supportedLanguages.first.code,
          onPressed: (lang) {
            cubit.onTranslateToLanguageWith(lang?.code ?? "en");
          },
        );
      default:
        break;
    }
  }

  Widget _buildTabItem(String entry) {
    return AnimatedBuilder(
      animation: _tabController.animation!,
      builder: (context, child) {
        final currentAnimationValue = _tabController.animation!.value;
        final tabIndex = _tabList.indexOf(entry);
        final double selectionStrength =
            (1 - (currentAnimationValue - tabIndex).abs()).clamp(0.0, 1.0);
        final isSelected = selectionStrength > 0.5;

        return Tab(
          height: 40.h,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  color: isSelected
                      ? context.colorScheme.mainBlue
                      : context.colorScheme.mainNeutral,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      entry,
                      style: TextStyle(
                        fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                        fontWeight: FontWeight.w500,
                        color: isSelected
                            ? context.colorScheme.themeWhite
                            : context.colorScheme.mainGray,
                      ),
                    ),
                  ],
                ),
              ),
              entry == S.current.slide_show
                  ? Positioned(
                      top: -4,
                      right: -6,
                      child: Transform.rotate(
                        angle: pi / 8,
                        child: IntrinsicWidth(
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              SvgPicture.asset(
                                Assets.images.imgTagNew,
                              ),
                              Positioned.fill(
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    top: 2.0,
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(100.r),
                                    child: Lottie.asset(
                                      Assets.videos.iapBtn,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ],
          ),
        );
      },
    );
  }

  BoxDecoration _buildTabIndicator() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(100.r),
      color: Colors.transparent,
    );
  }

  Widget _buildChatButton(BuildContext context, Box box) {
    return BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
        buildWhen: (previous, current) =>
            previous.isRefreshingSummary != current.isRefreshingSummary,
        builder: (context, state) {
          final note = cubit.observerBoxNote.value.get(widget.noteModel.id);
          if (!_shouldShowChatButton(note)) {
            return const SizedBox.shrink();
          }
          return Positioned(
            bottom: Platform.isAndroid
                ? MediaQuery.of(context).padding.bottom - 36.h
                : MediaQuery.of(context).padding.bottom -
                    (context.isTablet ? 16 : 48.h),
            left: 0.w,
            right: 0.w,
            child: buildChatTextField(
              context,
              onTap: () => _handleChatButtonTap(context, note),
              placeholderTexts: [
                S.current.ask_anything,
                ...note!.suggestions.map((e) => e.question)
              ],
            ),
          );
        });
  }

  bool _shouldShowChatButton(NoteModel? note) {
    return note != null &&
        !widget.isCommunityNote &&
        note.noteStatus == NoteStatus.success &&
        note.summary.isNotEmpty;
  }

  void _handleChatButtonTap(BuildContext context, NoteModel note) {
    AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_open_ai_chat);
    showChatBottomSheet(context, note);
  }

  Widget _buildOverflowMenuAction() {
    return ValueListenableBuilder(
      valueListenable: cubit.observerBoxNote,
      builder: (context, box, child) {
        final note = box.get(widget.noteModel.id) ??
            HiveService().noteFailedBox.get(widget.noteModel.id) ??
            widget.noteModel;
        _updateNoteData(note);
        return note.noteStatus == NoteStatus.loading
            ? const SizedBox.shrink()
            : _buildOverflowMenu(note);
      },
    );
  }

  void _updateNoteData(NoteModel note) {
    chooseFolderName.value =
        HiveFolderService.getNameFolderByBackEndId(note.folderId);
    cubit.setNoteInfo(note);
  }

  Widget _buildOverflowMenu(NoteModel note) {
    if (_sharingViewKey.currentState != null) {
      // Schedule update after the frame is built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _sharingViewKey.currentState?.updateNote(note);
      });
    }

    return NoteOverflowMenu(
      menuKey: _overflowMenuKey,
      note: note,
      cubit: cubit,
      isCommunityNote: widget.isCommunityNote,
      focusNode: _focusNodeTitle,
      onShowFolder: _showModalBottomSheetFolder,
      onShowBottomSheet: (exportType) {
        _showModalBottomSheetExport(
          context,
          exportType,
          note,
        );
      },
      onShowSharingView: Func0(() {
        _showSharingBottomSheet(note);
      }),
      tabList: _tabList,
    );
  }

  void _showModalBottomSheetExport(
    BuildContext context,
    ExportType exportType,
    NoteModel note,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: context.colorScheme.mainNeutral,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.85,
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      exportType.displayTitle,
                      style: TextStyle(
                        color: context.colorScheme.mainPrimary,
                        fontWeight: FontWeight.w600,
                        fontSize: context.isTablet ? 22 : 20.sp,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: SvgPicture.asset(
                        Assets.icons.icCloseWhite,
                        width: 24.w,
                        height: 24.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainGray,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: exportType
                      .getExportFormatList(widget.noteModel.type)
                      .length,
                  itemBuilder: (context, index) {
                    final format = exportType
                        .getExportFormatList(widget.noteModel.type)[index];
                    return Column(
                      children: [
                        ExportOptionItem(
                          text: format.displayName,
                          onTap: () {
                            final setInfoFlashcard =
                                _localService.getFlashcardSetInfo(
                              cubit.noteModel.backendNoteId,
                            );

                            final setInfoQuiz = _localService.getQuizSetInfo(
                              cubit.noteModel.backendNoteId,
                            );

                            Navigator.pop(context);

                            if (exportType == ExportType.quiz) {
                              final String setId = setInfoQuiz['setId'] ?? '';
                              final String title = setInfoQuiz['title'] ?? '';

                              // CHECK và lấy setId từ noteModel nếu cần
                              final String finalSetId = setId.isEmpty &&
                                      cubit.noteModel.quizSets.isNotEmpty
                                  ? cubit.noteModel.quizSets.last.setId
                                  : setId;

                              if (finalSetId.isEmpty) {
                                CommonDialogs.showToast(
                                  S.current.quiz_set_not_found,
                                  gravity: ToastGravity.BOTTOM,
                                  length: Toast.LENGTH_LONG,
                                );
                                return;
                              }

                              cubit.handleExportQuizSet(
                                format,
                                context.isTablet
                                    ? Rect.fromCenter(
                                        center: Offset(
                                          MediaQuery.of(context).size.width / 2,
                                          MediaQuery.of(context).size.height /
                                              2,
                                        ),
                                        width: 100,
                                        height: 100,
                                      )
                                    : null,
                                title,
                                finalSetId,
                                exportType,
                              );
                            } else if (exportType == ExportType.flashcards) {
                              final String setId =
                                  setInfoFlashcard['setId'] ?? '';
                              final String title =
                                  setInfoFlashcard['title'] ?? '';

                              // CHECK và lấy setId từ noteModel nếu cần
                              final String finalSetId = setId.isEmpty &&
                                      cubit.noteModel.flashcardSets.isNotEmpty
                                  ? cubit.noteModel.flashcardSets.last.setId
                                  : setId;

                              if (finalSetId.isEmpty) {
                                CommonDialogs.showToast(
                                  S.current.flashcard_set_not_found,
                                  gravity: ToastGravity.BOTTOM,
                                  length: Toast.LENGTH_LONG,
                                );
                                return;
                              }

                              cubit.handleExportFlashcardSet(
                                format,
                                context.isTablet
                                    ? Rect.fromCenter(
                                        center: Offset(
                                          MediaQuery.of(context).size.width / 2,
                                          MediaQuery.of(context).size.height /
                                              2,
                                        ),
                                        width: 100,
                                        height: 100,
                                      )
                                    : null,
                                title,
                                finalSetId,
                                exportType,
                              );
                            } else {
                              // Xử lý export summary và transcript
                              cubit.handleExportNote(
                                format,
                                exportType,
                                context.isTablet
                                    ? Rect.fromCenter(
                                        center: Offset(
                                          MediaQuery.of(context).size.width / 2,
                                          MediaQuery.of(context).size.height /
                                              2,
                                        ),
                                        width: 100,
                                        height: 100,
                                      )
                                    : null,
                              );
                            }
                          },
                        ),
                        if (format != exportType.exportFormatList.last)
                          AppConstants.kSpacingItem12,
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
    );
  }

  void _showSharingBottomSheet(NoteModel note) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.85,
      ),
      backgroundColor: context.colorScheme.mainNeutral,
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title bar with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      S.current.share_note_link,
                      style: TextStyle(
                        color: context.colorScheme.mainPrimary,
                        fontWeight: FontWeight.w600,
                        fontSize: cubit.appCubit.isTablet ? 22 : 20.sp,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: SvgPicture.asset(
                        Assets.icons.icCloseWhite,
                        width: 24.w,
                        height: 24.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainGray,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                // Use the updatable wrapper with a key
                UpdatableNoteSharingView(
                  key: _sharingViewKey,
                  note: note,
                  onPublicToggled: (isPublicToggled) {
                    cubit.updateNoteSharingSetting(isPublic: isPublicToggled);
                  },
                  onPasswordToggled: (isPasswordToggled) {
                    cubit.updateNoteSharingSetting(
                        isPasswordProtected: isPasswordToggled);
                  },
                  onLinkCopied: () {
                    CommonDialogs.showToast(S.current.copied_to_clipboard);
                  },
                  onPasswordCopied: () {
                    CommonDialogs.showToast(S.current.copied_to_clipboard);
                  },
                ),
                SizedBox(height: 16.h),
              ],
            ),
          ),
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
    );
  }

  /// Builds the action buttons in the app bar
  /// Shows either the quiz/flashcard button and done button or overflow menu
  Widget _buildActionButtons() {
    return Row(
      children: [
        AppConstants.kSpacingItemW8,
        // Tab-specific button for quiz/flashcard sets
        _buildQuizFlashcardSetButton(),
        AppConstants.kSpacingItemW6,
        // Done button or overflow menu based on edit state
        _buildEditActionButton(),
      ],
    );
  }

  /// Builds either the Done button (when there are edits) or the overflow menu
  Widget _buildEditActionButton() {
    return KeyboardVisibilityBuilder(
      builder: (context, isKeyboardVisible) {
        return BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
          buildWhen: (previous, current) =>
              isKeyboardVisible ||
              previous.hasEdits != current.hasEdits ||
              previous.isRefreshingSummary != current.isRefreshingSummary,
          builder: (context, state) {
            return state.hasEdits
                ? _buildDoneButton()
                : _buildOverflowMenuAction();
          },
        );
      },
    );
  }

  /// Builds the Done button that appears when there are unsaved changes
  Widget _buildDoneButton() {
    return TextButton(
      onPressed: _onDonePressed,
      child: CommonText(
        S.current.done_button_label,
        style: TextStyle(
          fontSize: context.isTablet ? 18 : 16.sp,
          fontWeight: FontWeight.w600,
          color: context.colorScheme.mainBlue,
        ),
      ),
    );
  }

  void _addNewFlashcard() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.flashcard_scr_add_flashcard_set_click,
    );
  }

  void _addNewQuiz() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.quizzes_scr_add_quiz_set_click,
    );
  }

  /// Builds the button for accessing flashcard or quiz sets based on the current tab
  Widget _buildQuizFlashcardSetButton() {
    return ValueListenableBuilder<int>(
      valueListenable: _tabIndexNotifier,
      builder: (context, currentTabIndex, _) {
        return ValueListenableBuilder(
          valueListenable: cubit.observerBoxNote,
          builder: (context, box, _) {
            final currentNote =
                box.get(widget.noteModel.id) ?? widget.noteModel;
            final flashcardIndex = _tabList.indexOf(S.current.flashcard);
            final quizIndex = _tabList.indexOf(S.current.quizzes);

            // Show flashcard sets button when on flashcard tab
            if (currentTabIndex == flashcardIndex) {
              return _buildSetsButton(
                itemCount: currentNote.flashCard.length,
                onTap: () =>
                    _navigateToFlashcardSets(currentNote, flashcardIndex),
              );
            }

            // Show quiz sets button when on quiz tab
            if (currentTabIndex == quizIndex) {
              return _buildSetsButton(
                itemCount: currentNote.quiz.length,
                onTap: () => _navigateToQuizSets(currentNote, quizIndex),
              );
            }

            return const SizedBox.shrink();
          },
        );
      },
    );
  }

  /// Builds a button for accessing sets (flashcards or quizzes)
  Widget _buildSetsButton(
      {required int itemCount, required VoidCallback onTap}) {
    if (itemCount == 0) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: SvgPicture.asset(
        Assets.icons.icSets,
        colorFilter: ColorFilter.mode(
          context.colorScheme.mainPrimary,
          BlendMode.srcIn,
        ),
      ),
    );
  }

  /// Navigates to the flashcard sets page
  void _navigateToFlashcardSets(NoteModel note, int flashcardIndex) {
    _addNewFlashcard();
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (_) => FlashcardsSetsPage(
          noteModel: note,
          isCommunityNote: widget.isCommunityNote,
          parentCubit: cubit,
        ),
        fullscreenDialog: true,
      ),
    ).then((result) {
      if (result is Map &&
          result['selectFlashcardTab'] == true &&
          flashcardIndex != -1) {
        _tabController.animateTo(flashcardIndex);
      }
    });
  }

  /// Navigates to the quiz sets page
  void _navigateToQuizSets(NoteModel note, int quizIndex) {
    _addNewQuiz();
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (_) => QuizSetsPage(
          noteModel: note,
          isCommunityNote: widget.isCommunityNote,
          parentCubit: cubit,
        ),
        fullscreenDialog: true,
      ),
    ).then((result) {
      if (result is Map && result['selectQuizTab'] == true && quizIndex != -1) {
        _tabController.animateTo(quizIndex);
      }
    });
  }

  void _onDonePressed() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_detail_done_button_pressed,
    );

    final noteLocal = cubit.observerBoxNote.value.get(widget.noteModel.id);
    if (noteLocal == null) {
      FocusManager.instance.primaryFocus?.unfocus();
      return;
    }

    final deltaToMarkdown = DeltaToMarkdown();
    final markdown = deltaToMarkdown
        .convert(cubit.getQuillSummaryController.document.toDelta());
    final transcript = cubit.getQuillTranscriptController.document.getPlainText(
        0, cubit.getQuillTranscriptController.document.length - 1);
    final newTitle = cubit.getTitleController.text;

    final bool hasSummaryChanged = markdown != noteLocal.summary;
    final bool hasTitleChanged = newTitle != noteLocal.title;
    final bool hasTranscriptChanged = transcript != noteLocal.transcript;

    final bool isTranscriptTab = _tabController.index == 1;
    List<TranscriptModel>? updatedTranscriptJson;

    if (isTranscriptTab || hasTranscriptChanged) {
      updatedTranscriptJson = cubit.convertDeltaToTranscriptJson(
          cubit.getQuillTranscriptController.document.toDelta(),
          noteLocal.transcriptJson);
    }

    if (hasSummaryChanged ||
        hasTitleChanged ||
        hasTranscriptChanged ||
        updatedTranscriptJson != null) {
      cubit.onEditNote(
        note: noteLocal,
        summary: hasSummaryChanged ? markdown : null,
        transcript: hasTranscriptChanged ? transcript : null,
        transcriptJson: updatedTranscriptJson,
        title: hasTitleChanged ? newTitle : null,
      );
    } else {
      cubit.setHasEdits(false);
    }

    SearchToolbar.hide();
    FocusManager.instance.primaryFocus?.unfocus();
  }
}
