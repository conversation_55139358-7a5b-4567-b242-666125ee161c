import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:just_audio/just_audio.dart';
import 'package:note_x/lib.dart';

class TabTranscriptWidget extends StatelessWidget {
  const TabTranscriptWidget({
    super.key,
    required this.isCommunityNote,
    required this.noteModel,
    required this.quillTranscriptController,
    required this.cubit,
    this.audioPlayer,
  });

  final NoteModel noteModel;
  final bool isCommunityNote;
  final QuillController quillTranscriptController;
  final AudioPlayer? audioPlayer;
  final MyNoteDetailCubit cubit;

  @override
  Widget build(BuildContext context) {
    return isCommunityNote
        ? _buildTranscriptPage(noteModel, context)
        : ValueListenableBuilder(
            valueListenable: HiveService().noteBox.listenable(),
            builder: (context, box, child) {
              final note = box.get(noteModel.id) ??
                  HiveService().noteFailedBox.get(noteModel.id);
              if (note == null) {
                return const SizedBox.shrink();
              }
              return _buildTranscriptPage(note, context);
            });
  }

  Widget _buildTranscriptPage(NoteModel note, BuildContext context) {
    return TranscriptPage(
      isCommunityNote: isCommunityNote,
      quillTranscriptController: quillTranscriptController,
      note: note,
      transcriptJson: note.transcriptJson,
      transcript: note.transcript,
      audioPlayer: audioPlayer,
      cubit: cubit,
    );
  }
}
